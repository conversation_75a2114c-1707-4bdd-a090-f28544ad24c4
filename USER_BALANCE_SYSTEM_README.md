# 用户余额系统实施指南

## 概述

本文档描述了为校园二手书交易系统添加用户余额功能的完整实施方案。该系统实现了用户余额管理、交易时的资金流转、余额验证等核心功能。

## 功能特性

### 核心功能
1. **用户余额管理**
   - 用户注册时自动设置初始余额为0
   - 支持余额查询
   - 支持充值功能（模拟）

2. **交易资金流转**
   - 购买时从买家余额扣款
   - 销售时向卖家余额增加收入
   - 订单取消时自动退款

3. **余额验证**
   - 支付前检查余额是否足够
   - 余额不足时提示用户充值

4. **余额记录**
   - 记录所有余额变动
   - 支持查看余额变动明细
   - 包含变动类型、金额、时间等信息

## 技术实现

### 后端实现

#### 1. 数据库变更
- **用户表（sh_user）**：添加 `balance` 字段（DECIMAL(10,2)）
- **新增余额记录表（sh_balance_record）**：记录所有余额变动

#### 2. 新增模型类
- `BalanceRecordModel`：余额变动记录实体

#### 3. 新增服务层
- `BalanceService`：余额管理服务接口
- `BalanceServiceImpl`：余额管理服务实现

#### 4. 新增控制器
- `BalanceController`：余额相关API接口

#### 5. 修改现有服务
- `UserService`：添加余额查询方法
- `OrderService`：集成余额验证和资金流转逻辑

### 前端实现

#### 1. 新增页面
- `balance.vue`：余额管理页面
  - 显示当前余额
  - 充值功能
  - 余额变动记录查看

#### 2. 修改现有页面
- `me.vue`：个人中心显示余额信息
- `order.vue`：支付时检查余额并使用余额支付

#### 3. 新增API接口
- 余额查询、充值、记录查询等API调用

## 安装和部署

### 1. 数据库迁移
执行 `database_migration.sql` 脚本：

```sql
-- 为用户表添加余额字段
ALTER TABLE sh_user ADD COLUMN balance DECIMAL(10,2) DEFAULT 0.00 COMMENT '用户余额';

-- 为现有用户设置初始余额
UPDATE sh_user SET balance = 0.00 WHERE balance IS NULL;

-- 创建余额变动记录表
CREATE TABLE sh_balance_record (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
    user_id BIGINT NOT NULL COMMENT '用户主键id',
    record_type TINYINT NOT NULL COMMENT '变动类型（1-充值，2-购买扣款，3-销售收入，4-退款）',
    amount DECIMAL(10,2) NOT NULL COMMENT '变动金额',
    balance_before DECIMAL(10,2) NOT NULL COMMENT '变动前余额',
    balance_after DECIMAL(10,2) NOT NULL COMMENT '变动后余额',
    order_id BIGINT NULL COMMENT '关联订单ID',
    description VARCHAR(255) NULL COMMENT '变动描述',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_user_id (user_id),
    INDEX idx_order_id (order_id),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户余额变动记录表';
```

### 2. 后端部署
1. 确保所有新增的Java文件已编译
2. 重启Spring Boot应用

### 3. 前端部署
1. 确保新增的Vue组件和路由配置正确
2. 重新构建前端应用：`npm run build`

## API接口文档

### 余额相关接口

#### 1. 获取用户余额
- **URL**: `/balance/info`
- **方法**: GET
- **返回**: 用户当前余额

#### 2. 充值
- **URL**: `/balance/recharge`
- **方法**: POST
- **参数**: `amount` - 充值金额
- **返回**: 充值结果

#### 3. 获取余额记录
- **URL**: `/balance/records`
- **方法**: GET
- **参数**: `page`, `nums` - 分页参数
- **返回**: 余额变动记录列表

#### 4. 检查余额
- **URL**: `/balance/check`
- **方法**: GET
- **参数**: `amount` - 需要检查的金额
- **返回**: 余额是否足够

## 使用说明

### 用户操作流程

1. **查看余额**
   - 在个人中心页面可以看到当前余额
   - 点击"管理余额"进入余额管理页面

2. **充值**
   - 在余额管理页面点击"充值"
   - 输入充值金额（1-10000元）
   - 确认充值（模拟充值，实际项目中需要接入支付网关）

3. **购买商品**
   - 创建订单后，在订单页面点击"立即支付"
   - 系统自动检查余额是否足够
   - 余额足够时使用余额支付
   - 余额不足时提示充值

4. **查看余额记录**
   - 在余额管理页面点击"查看明细"
   - 可以看到所有余额变动记录

### 管理员功能
- 可以查看用户余额信息
- 可以查看系统中的交易记录

## 注意事项

1. **数据一致性**
   - 所有余额操作都使用事务确保数据一致性
   - 余额变动和记录插入在同一事务中完成

2. **安全性**
   - 余额操作需要用户登录验证
   - 支付前会再次验证余额

3. **性能考虑**
   - 余额查询使用索引优化
   - 余额记录表按时间和用户ID建立索引

4. **扩展性**
   - 余额记录类型可以扩展
   - 支持未来添加更多支付方式

## 测试建议

1. **功能测试**
   - 测试用户注册时余额初始化
   - 测试充值功能
   - 测试购买时余额扣减
   - 测试销售时余额增加
   - 测试订单取消时退款

2. **边界测试**
   - 测试余额不足时的处理
   - 测试充值金额限制
   - 测试并发支付场景

3. **数据一致性测试**
   - 测试事务回滚场景
   - 测试余额记录的准确性

## 后续优化建议

1. **支付网关集成**
   - 接入真实的支付平台（支付宝、微信支付等）

2. **风控系统**
   - 添加异常交易检测
   - 添加余额变动限制

3. **报表功能**
   - 添加财务报表
   - 添加交易统计分析

4. **通知功能**
   - 余额变动通知
   - 余额不足提醒

## 联系信息

如有问题或建议，请联系开发团队。
