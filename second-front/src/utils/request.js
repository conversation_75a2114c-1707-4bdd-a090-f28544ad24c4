import axios from 'axios';

const service = axios.create({
    timeout: 5000,
    baseURL:  'http://localhost:8080',
    withCredentials:  true
});

service.interceptors.request.use(
    config => {
        return config;
    },
    error => {
        console.log(error);
        return Promise.reject();
    }
);

service.interceptors.response.use(
    response => {
        if (response.status === 200) {
            return response.data;
        } else {
            Promise.reject();
        }
    },
    error => {
        console.error('请求错误详情:', error);
        if (error.code === 'ECONNREFUSED' || error.message.includes('Network Error')) {
            console.error('无法连接到后端服务，请检查后端是否启动在 http://localhost:8080');
        }
        return Promise.reject(error);
    }
);

export default service;
