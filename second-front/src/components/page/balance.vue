<template>
    <div>
        <app-head :nickname-value="userInfo.nickname" :avatarValue="userInfo.avatar"></app-head>
        <app-page-body>
            <div class="balance-container">
                <div class="balance-header">
                    <h2>我的余额</h2>
                    <div class="balance-amount">
                        <span class="currency">¥</span>
                        <span class="amount">{{ balance }}</span>
                    </div>
                    <div class="balance-actions">
                        <el-button type="primary" @click="showRechargeDialog = true">充值</el-button>
                        <el-button @click="getRecords">查看明细</el-button>
                    </div>
                </div>

                <!-- 充值对话框 -->
                <el-dialog title="账户充值" :visible.sync="showRechargeDialog" width="400px">
                    <div class="recharge-form">
                        <div class="recharge-amount">
                            <label>充值金额：</label>
                            <el-input-number
                                v-model="rechargeAmount"
                                :min="1"
                                :max="10000"
                                :precision="2"
                                placeholder="请输入充值金额">
                            </el-input-number>
                        </div>
                        <div class="quick-amounts">
                            <span>快捷金额：</span>
                            <el-button size="small" @click="rechargeAmount = 50">50</el-button>
                            <el-button size="small" @click="rechargeAmount = 100">100</el-button>
                            <el-button size="small" @click="rechargeAmount = 200">200</el-button>
                            <el-button size="small" @click="rechargeAmount = 500">500</el-button>
                        </div>
                    </div>
                    <span slot="footer" class="dialog-footer">
                        <el-button @click="showRechargeDialog = false">取消</el-button>
                        <el-button type="primary" @click="doRecharge" :loading="recharging">确认充值</el-button>
                    </span>
                </el-dialog>

                <!-- 余额明细 -->
                <div class="balance-records" v-if="showRecords">
                    <h3>余额明细</h3>
                    <el-table :data="records" style="width: 100%">
                        <el-table-column prop="createTime" label="时间" width="180">
                            <template slot-scope="scope">
                                {{ formatDate(scope.row.createTime) }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="description" label="描述" width="200"></el-table-column>
                        <el-table-column prop="amount" label="金额" width="120">
                            <template slot-scope="scope">
                                <span :class="scope.row.amount >= 0 ? 'positive' : 'negative'">
                                    {{ scope.row.amount >= 0 ? '+' : '' }}{{ scope.row.amount }}
                                </span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="balanceAfter" label="余额" width="120"></el-table-column>
                        <el-table-column prop="recordType" label="类型" width="100">
                            <template slot-scope="scope">
                                <el-tag :type="getRecordTypeColor(scope.row.recordType)">
                                    {{ getRecordTypeName(scope.row.recordType) }}
                                </el-tag>
                            </template>
                        </el-table-column>
                    </el-table>
                    
                    <el-pagination
                        @current-change="handlePageChange"
                        :current-page="currentPage"
                        :page-size="pageSize"
                        layout="total, prev, pager, next"
                        :total="totalRecords">
                    </el-pagination>
                </div>
            </div>
        </app-page-body>
    </div>
</template>

<script>
import AppHead from '../common/AppHeader'
import AppPageBody from '../common/AppPageBody'

export default {
    name: "balance",
    components: {
        AppHead,
        AppPageBody
    },
    data() {
        return {
            userInfo: {},
            balance: 0,
            showRechargeDialog: false,
            rechargeAmount: 100,
            recharging: false,
            showRecords: false,
            records: [],
            currentPage: 1,
            pageSize: 10,
            totalRecords: 0
        };
    },
    created() {
        if (!this.$globalData.userInfo.nickname) {
            this.$api.getUserInfo().then(res => {
                if (res.status_code === 1) {
                    res.data.signInTime = res.data.signInTime.substring(0, 10);
                    this.$globalData.userInfo = res.data;
                    this.userInfo = this.$globalData.userInfo;
                }
            })
        } else {
            this.userInfo = this.$globalData.userInfo;
        }
        this.getBalance();
    },
    methods: {
        getBalance() {
            this.$api.getBalance().then(res => {
                if (res.status_code === 1) {
                    this.balance = res.data;
                } else {
                    this.$message.error(res.msg);
                }
            }).catch(e => {
                this.$message.error('获取余额失败');
            });
        },
        doRecharge() {
            if (!this.rechargeAmount || this.rechargeAmount <= 0) {
                this.$message.error('请输入有效的充值金额');
                return;
            }
            
            this.recharging = true;
            const formData = new FormData();
            formData.append('amount', this.rechargeAmount);
            
            this.$api.recharge(formData).then(res => {
                this.recharging = false;
                if (res.status_code === 1) {
                    this.$message.success('充值成功');
                    this.showRechargeDialog = false;
                    this.getBalance();
                    this.rechargeAmount = 100;
                } else {
                    this.$message.error(res.msg || '充值失败');
                }
            }).catch(e => {
                this.recharging = false;
                this.$message.error('充值失败');
            });
        },
        getRecords() {
            this.showRecords = true;
            this.$api.getBalanceRecords({
                page: this.currentPage,
                nums: this.pageSize
            }).then(res => {
                if (res.status_code === 1) {
                    this.records = res.data.list;
                    this.totalRecords = res.data.count;
                } else {
                    this.$message.error(res.msg);
                }
            }).catch(e => {
                this.$message.error('获取记录失败');
            });
        },
        handlePageChange(page) {
            this.currentPage = page;
            this.getRecords();
        },
        formatDate(dateStr) {
            return new Date(dateStr).toLocaleString();
        },
        getRecordTypeName(type) {
            const types = {
                1: '充值',
                2: '购买',
                3: '销售',
                4: '退款'
            };
            return types[type] || '未知';
        },
        getRecordTypeColor(type) {
            const colors = {
                1: 'success',
                2: 'warning',
                3: 'success',
                4: 'info'
            };
            return colors[type] || '';
        }
    }
}
</script>

<style scoped>
.balance-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.balance-header {
    text-align: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    border-radius: 10px;
    margin-bottom: 30px;
}

.balance-amount {
    margin: 20px 0;
}

.currency {
    font-size: 24px;
    margin-right: 5px;
}

.amount {
    font-size: 48px;
    font-weight: bold;
}

.balance-actions {
    margin-top: 20px;
}

.balance-actions .el-button {
    margin: 0 10px;
}

.recharge-form {
    padding: 20px 0;
}

.recharge-amount {
    margin-bottom: 20px;
}

.recharge-amount label {
    display: inline-block;
    width: 80px;
    margin-right: 10px;
}

.quick-amounts {
    margin-top: 15px;
}

.quick-amounts span {
    margin-right: 10px;
}

.quick-amounts .el-button {
    margin-right: 10px;
}

.balance-records {
    margin-top: 30px;
}

.positive {
    color: #67C23A;
}

.negative {
    color: #F56C6C;
}

.el-pagination {
    text-align: center;
    margin-top: 20px;
}
</style>
