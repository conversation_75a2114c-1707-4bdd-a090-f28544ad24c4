<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.second.hand.trading.server.dao.UserDao">
  <resultMap id="BaseResultMap" type="com.second.hand.trading.server.model.UserModel">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="account_number" jdbcType="VARCHAR" property="accountNumber" />
    <result column="user_password" jdbcType="VARCHAR" property="userPassword" />
    <result column="nickname" jdbcType="VARCHAR" property="nickname" />
    <result column="avatar" jdbcType="VARCHAR" property="avatar" />
    <result column="sign_in_time" jdbcType="TIMESTAMP" property="signInTime" />
    <result column="balance" jdbcType="DECIMAL" property="balance" />
    <result column="user_status" jdbcType="TINYINT" property="userStatus" />
  </resultMap>
  <sql id="Base_Column_List">
    id, account_number, nickname, avatar, sign_in_time, user_status, IFNULL(balance, 0.00) as balance
  </sql>
  <select id="userLogin" resultMap="BaseResultMap">
    select id, account_number, nickname, avatar, sign_in_time, user_status,
           IFNULL(balance, 0.00) as balance
    from sh_user
    where account_number = #{accountNumber} and user_password = #{userPassword}
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sh_user
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="countNormalUser" resultType="int">
    select count(*) from sh_user
    where user_status is null or user_status = 0
  </select>
  <select id="countBanUser" resultType="int">
    select count(*) from sh_user
    where user_status = 1
  </select>
  <select id="getUserList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from sh_user
  </select>
  <select id="getNormalUser" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from sh_user where user_status is null or user_status = 0 order by id desc limit #{begin}, #{nums}
  </select>
  <select id="getBanUser" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from sh_user where user_status = 1 order by id desc limit #{begin}, #{nums}
  </select>
  <select id="findUserByList" resultMap="BaseResultMap">
    select id,nickname, avatar, sign_in_time, IFNULL(balance, 0.00) as balance
    from sh_user where id in
    <foreach collection="idList" index="index" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from sh_user
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.second.hand.trading.server.model.UserModel" useGeneratedKeys="true">
    insert into sh_user (account_number, user_password, nickname, 
      avatar, sign_in_time)
    values (#{accountNumber,jdbcType=VARCHAR}, #{userPassword,jdbcType=VARCHAR}, #{nickname,jdbcType=VARCHAR}, 
      #{avatar,jdbcType=VARCHAR}, #{signInTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.second.hand.trading.server.model.UserModel" useGeneratedKeys="true">
    insert into sh_user
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accountNumber != null">
        account_number,
      </if>
      <if test="userPassword != null">
        user_password,
      </if>
      <if test="nickname != null">
        nickname,
      </if>
      <if test="avatar != null">
        avatar,
      </if>
      <if test="signInTime != null">
        sign_in_time,
      </if>
      <if test="balance != null">
        balance,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accountNumber != null">
        #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="userPassword != null">
        #{userPassword,jdbcType=VARCHAR},
      </if>
      <if test="nickname != null">
        #{nickname,jdbcType=VARCHAR},
      </if>
      <if test="avatar != null">
        #{avatar,jdbcType=VARCHAR},
      </if>
      <if test="signInTime != null">
        #{signInTime,jdbcType=TIMESTAMP},
      </if>
      <if test="balance != null">
        #{balance,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.second.hand.trading.server.model.UserModel">
    update sh_user
    <set>
      <if test="accountNumber != null">
        account_number = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="userPassword != null">
        user_password = #{userPassword,jdbcType=VARCHAR},
      </if>
      <if test="nickname != null">
        nickname = #{nickname,jdbcType=VARCHAR},
      </if>
      <if test="avatar != null">
        avatar = #{avatar,jdbcType=VARCHAR},
      </if>
      <if test="signInTime != null">
        sign_in_time = #{signInTime,jdbcType=TIMESTAMP},
      </if>
      <if test="userStatus != null">
        user_status = #{userStatus},
      </if>
      <if test="balance != null">
        balance = #{balance,jdbcType=DECIMAL},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.second.hand.trading.server.model.UserModel">
    update sh_user
    set account_number = #{accountNumber,jdbcType=VARCHAR},
      user_password = #{userPassword,jdbcType=VARCHAR},
      nickname = #{nickname,jdbcType=VARCHAR},
      avatar = #{avatar,jdbcType=VARCHAR},
      sign_in_time = #{signInTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updatePassword">
    update sh_user
    set user_password = #{newPassword,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT} and user_password=#{oldPassword,jdbcType=VARCHAR}
  </update>
  <update id="updateBalance">
    update sh_user
    set balance = #{balance,jdbcType=DECIMAL}
    where id = #{userId,jdbcType=BIGINT}
  </update>
  <select id="getUserBalance" parameterType="java.lang.Long" resultType="java.math.BigDecimal">
    select IFNULL(balance, 0.00) as balance from sh_user
    where id = #{userId,jdbcType=BIGINT}
  </select>
</mapper>