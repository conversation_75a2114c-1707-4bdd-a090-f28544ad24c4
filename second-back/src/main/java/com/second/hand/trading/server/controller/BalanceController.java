package com.second.hand.trading.server.controller;

import com.second.hand.trading.server.enums.ErrorMsg;
import com.second.hand.trading.server.service.BalanceService;
import com.second.hand.trading.server.vo.ResultVo;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@RestController
@RequestMapping("/balance")
public class BalanceController {

    @Resource
    private BalanceService balanceService;

    /**
     * 获取用户余额
     */
    @GetMapping("/info")
    public ResultVo getBalance(@CookieValue("shUserId")
                               @NotNull(message = "登录异常 请重新登录")
                               @NotEmpty(message = "登录异常 请重新登录") String shUserId) {
        BigDecimal balance = balanceService.getUserBalance(Long.valueOf(shUserId));
        return ResultVo.success(balance);
    }

    /**
     * 充值
     */
    @PostMapping("/recharge")
    public ResultVo recharge(@CookieValue("shUserId")
                             @NotNull(message = "登录异常 请重新登录")
                             @NotEmpty(message = "登录异常 请重新登录") String shUserId,
                             @RequestParam BigDecimal amount) {
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            return ResultVo.fail(ErrorMsg.PARAM_ERROR);
        }
        
        if (amount.compareTo(new BigDecimal("10000")) > 0) {
            return ResultVo.fail("单次充值金额不能超过10000元");
        }
        
        boolean success = balanceService.recharge(Long.valueOf(shUserId), amount, "用户充值");
        if (success) {
            return ResultVo.success("充值成功");
        }
        return ResultVo.fail(ErrorMsg.SYSTEM_ERROR);
    }

    /**
     * 获取余额变动记录
     */
    @GetMapping("/records")
    public ResultVo getBalanceRecords(@CookieValue("shUserId")
                                      @NotNull(message = "登录异常 请重新登录")
                                      @NotEmpty(message = "登录异常 请重新登录") String shUserId,
                                      @RequestParam(defaultValue = "1") int page,
                                      @RequestParam(defaultValue = "10") int nums) {
        return ResultVo.success(balanceService.getBalanceRecords(Long.valueOf(shUserId), page, nums));
    }

    /**
     * 检查余额是否足够
     */
    @GetMapping("/check")
    public ResultVo checkBalance(@CookieValue("shUserId")
                                 @NotNull(message = "登录异常 请重新登录")
                                 @NotEmpty(message = "登录异常 请重新登录") String shUserId,
                                 @RequestParam BigDecimal amount) {
        boolean sufficient = balanceService.checkBalance(Long.valueOf(shUserId), amount);
        return ResultVo.success(sufficient);
    }
}
